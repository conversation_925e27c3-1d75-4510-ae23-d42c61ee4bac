import axios from 'axios';

class AIManager {
  constructor() {
    this.apiUrl = 'https://gateway.chat.sensedeal.vip/v1';
    this.apiKey = '974fd8d1c155aa3d04b17bf253176b5e';
    this.model = 'qwen2.5-32b-instruct-int4';

    this.axiosInstance = axios.create({
      baseURL: this.apiUrl,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 600000 // 10分钟超时
    });
  }

  async testConnection() {
    try {
      const response = await this.axiosInstance.get('/models');
      console.log('AI API连接成功');
      console.log('可用模型:', response.data.data?.map(model => model.id) || []);
      return { success: true, models: response.data.data };
    } catch (error) {
      console.error('AI API连接失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  async generateResponse(prompt, systemPrompt = null) {
    try {
      const messages = [];

      if (systemPrompt) {
        messages.push({
          role: 'system',
          content: systemPrompt
        });
      }

      messages.push({
        role: 'user',
        content: prompt
      });

      const requestData = {
        model: this.model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 2000,
        stream: false
      };

      console.log('发送AI请求...');
      const response = await this.axiosInstance.post('/chat/completions', requestData);

      const aiResponse = response.data.choices[0].message.content;
      console.log('AI响应成功');

      return {
        success: true,
        response: aiResponse,
        usage: response.data.usage
      };
    } catch (error) {
      console.error('AI请求失败:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }

  async generateFileOperationPlan(userRequest, currentFiles = []) {
    const systemPrompt = `你是一个专业的代码助手，专门帮助用户在容器环境中进行文件操作。
用户会描述他们想要做的事情，你需要分析并生成具体的文件操作计划。

请以JSON格式返回操作计划，包含以下字段：
{
  "analysis": "对用户需求的分析",
  "operations": [
    {
      "type": "create|read|write|delete|list",
      "path": "文件或目录路径",
      "content": "文件内容（仅用于write操作）",
      "description": "操作描述"
    }
  ],
  "explanation": "操作步骤的详细说明"
}

当前工作目录中的文件: ${currentFiles.join(', ') || '无'}`;

    return await this.generateResponse(userRequest, systemPrompt);
  }

  async generateCodeExecutionPlan(userRequest, availableFiles = []) {
    const systemPrompt = `你是一个专业的代码执行助手，帮助用户在容器环境中执行代码。
用户会描述他们想要运行的代码或命令，你需要分析并生成具体的执行计划。

请以JSON格式返回执行计划，包含以下字段：
{
  "analysis": "对用户需求的分析",
  "commands": [
    {
      "command": "要执行的命令",
      "workingDir": "工作目录（可选）",
      "description": "命令描述",
      "expectedOutput": "预期输出描述"
    }
  ],
  "prerequisites": "执行前的准备工作",
  "explanation": "执行步骤的详细说明"
}

当前可用的文件: ${availableFiles.join(', ') || '无'}`;

    return await this.generateResponse(userRequest, systemPrompt);
  }

  async analyzeCode(code, language = 'auto') {
    const systemPrompt = `你是一个代码分析专家，请分析提供的代码并给出详细的分析报告。
包括：
1. 代码功能说明
2. 潜在问题或改进建议
3. 运行要求（依赖、环境等）
4. 预期输出

请以JSON格式返回分析结果：
{
  "language": "检测到的编程语言",
  "functionality": "代码功能描述",
  "issues": ["潜在问题列表"],
  "suggestions": ["改进建议列表"],
  "requirements": ["运行要求列表"],
  "expectedOutput": "预期输出描述"
}`;

    return await this.generateResponse(`请分析以下${language}代码：\n\n${code}`, systemPrompt);
  }

  async generateCodeFromDescription(description, language = 'python') {
    const systemPrompt = `你是一个专业的程序员，根据用户的描述生成高质量的${language}代码。
请确保代码：
1. 功能完整
2. 有适当的注释
3. 遵循最佳实践
4. 包含错误处理

请直接返回代码，不需要额外的解释文字。`;

    return await this.generateResponse(description, systemPrompt);
  }
}

export default AIManager;