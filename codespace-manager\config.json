{"ai": {"apiUrl": "https://gateway.chat.sensedeal.vip/v1", "apiKey": "974fd8d1c155aa3d04b17bf253176b5e", "model": "qwen2.5-32b-instruct-int4", "timeout": 600000, "maxTokens": 2000, "temperature": 0.7}, "docker": {"image": "gitpod/openvscode-server:latest", "containerPrefix": "codespace-", "portRange": {"min": 3001, "max": 9999}, "workspaceMount": "/home/<USER>", "autoRemove": false}, "system": {"dataDirectory": "./data", "workspacesDirectory": "./workspaces", "usersFile": "users.json", "containersFile": "containers.json"}, "security": {"bcryptRounds": 10, "minPasswordLength": 6}, "execution": {"defaultTimeout": 600000, "maxTimeout": 600000, "supportedLanguages": ["python", "javascript", "java", "cpp", "c", "bash", "sh"]}, "ui": {"colors": {"primary": "blue", "success": "green", "error": "red", "warning": "yellow", "info": "cyan"}, "defaultWorkingDirectory": "/home/<USER>"}}